{"version": 3, "file": "lsp-client.js", "sourceRoot": "", "sources": ["../src/lsp-client.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAoD;AACpD,mCAAsC;AACtC,2CAA6B;AAC7B,8CAK6B;AAG7B,mCAAoC;AAEpC,MAAa,SAAU,SAAQ,qBAAY;IASzC,YACU,aAAqB,EACrB,YAA6B,EACrC,QAAgB;QAEhB,KAAK,EAAE,CAAC;QAJA,kBAAa,GAAb,aAAa,CAAQ;QACrB,iBAAY,GAAZ,YAAY,CAAiB;QAV/B,YAAO,GAAwB,IAAI,CAAC;QACpC,eAAU,GAA6B,IAAI,CAAC;QAC5C,kBAAa,GAAG,KAAK,CAAC;QACtB,mBAAc,GAAG,KAAK,CAAC;QAW7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACxC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,KAAK;QAChB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,KAAK,CAAC,wCAAwC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC7E,CAAC,EAAE,KAAK,CAAC,CAAC;YAEV,IAAI,CAAC;gBACH,MAAM,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;gBACrD,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,MAAM,CAAC,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC,CAAC;oBAC3D,OAAO;gBACT,CAAC;gBAED,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;gBAE7D,IAAI,CAAC,OAAO,GAAG,IAAA,qBAAK,EAAC,OAAO,EAAE,OAAO,EAAE;oBACrC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;oBAC/B,GAAG,EAAE,IAAI,CAAC,aAAa;oBACvB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE;oBACjD,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBACjC,OAAO,CAAC,KAAK,CAAC,iCAAiC,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;oBACxE,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,IAAI,CAAC,OAAO,EAAE,CAAC;oBACf,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACvC,OAAO,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBACnD,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;oBACvC,OAAO,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,QAAQ,qBAAqB,IAAI,YAAY,MAAM,EAAE,CAAC,CAAC;oBAC/F,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;wBACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;oBAClC,CAAC;oBACD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,UAAU,GAAG,IAAA,8BAAuB,EACvC,IAAI,0BAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC,EAC7C,IAAI,0BAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAM,CAAC,CAC7C,CAAC;gBAEF,IAAI,gBAAgC,CAAC;gBAErC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,MAAsB,EAAE,EAAE;oBACtE,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;wBACtD,OAAO,CAAC,KAAK,CAAC,gDAAgD,IAAI,CAAC,QAAQ,qBAAqB,CAAC,CAAC;wBAClG,IAAI,gBAAgB;4BAAE,YAAY,CAAC,gBAAgB,CAAC,CAAC;wBACrD,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBAEzB,IAAI,CAAC,UAAU,EAAE;qBACd,IAAI,CAAC,GAAG,EAAE;oBACT,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC1B,+DAA+D;oBAC/D,gBAAgB,GAAG,UAAU,CAAC,GAAG,EAAE;wBAC/B,OAAO,CAAC,KAAK,CAAC,gDAAgD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;wBAChF,IAAI,CAAC,YAAY,EAAE,CAAA;oBACvB,CAAC,EAAE,IAAI,CAAC,CAAC;oBACT,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC1E,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,IAAI,CAAC,OAAO,EAAE,CAAC;oBACf,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC;YAEP,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QACD,MAAM,OAAO,GAAG,IAAA,iBAAS,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAqB;YAC/B,SAAS,EAAE,OAAO,CAAC,GAAG;YACtB,UAAU,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE,OAAO,EAAE,OAAO,EAAE;YAChE,OAAO,EAAE,OAAO;YAChB,gBAAgB,EAAE,CAAC;oBACjB,GAAG,EAAE,OAAO;oBACZ,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,YAAY;iBACnC,CAAC;YACF,YAAY,EAAE;gBACZ,YAAY,EAAE;oBACZ,eAAe,EAAE;wBACf,mBAAmB,EAAE,KAAK;wBAC1B,QAAQ,EAAE,KAAK;wBACf,iBAAiB,EAAE,KAAK;wBACxB,OAAO,EAAE,IAAI;qBACd;oBACD,UAAU,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE;oBAClC,UAAU,EAAE;wBACV,mBAAmB,EAAE,KAAK;qBAC3B;oBACD,MAAM,EAAE,EAAE,cAAc,EAAE,KAAK,EAAE;oBACjC,cAAc,EAAE;wBACd,UAAU,EAAE;4BACV,QAAQ,EAAE;gCACR,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gCACjF,EAAE,EAAE,EAAE,EAAE,EAAE;6BACX;yBACF;wBACD,iCAAiC,EAAE,IAAI;qBACxC;oBACD,UAAU,EAAE;wBACV,cAAc,EAAE;4BACd,cAAc,EAAE,IAAI;yBACrB;qBACF;oBACD,aAAa,EAAE,EAAE;oBACjB,UAAU,EAAE;wBACV,mBAAmB,EAAE,KAAK;wBAC1B,sBAAsB,EAAE,KAAK;qBAC9B;iBACF;gBACD,SAAS,EAAE;oBACT,gBAAgB,EAAE,IAAI;iBACvB;aACF;YACD,qBAAqB,EAAE;gBACrB,QAAQ,EAAE,EAAE;aACb;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QACxD,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;IACxD,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,GAAW,EAAE,OAAe,EAAE,UAAkB;QACxE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,MAAM,GAA8B;YACxC,YAAY,EAAE;gBACZ,GAAG;gBACH,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,CAAC;gBACV,IAAI,EAAE,OAAO;aACM;SACtB,CAAC;QACF,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;IACzE,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,GAAW,EAAE,QAAkB;QACxD,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAClE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAA+B,yBAAyB,EAAE;gBACxG,YAAY,EAAE,EAAE,GAAG,EAAE;gBACrB,QAAQ;aACT,CAAC,CAAC;YACH,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,GAAW,EAAE,QAAkB;QACxD,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAClE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAoB,yBAAyB,EAAE;gBAC7F,YAAY,EAAE,EAAE,GAAG,EAAE;gBACrB,QAAQ;gBACR,OAAO,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE;aACtC,CAAC,CAAC;YACH,OAAO,MAAM,IAAI,EAAE,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1F,MAAM,KAAK,CAAC,CAAC,8BAA8B;QAC7C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,GAAW;QACzC,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAClE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAA0B,6BAA6B,EAAE;gBACvG,YAAY,EAAE,EAAE,GAAG,EAAE;aACtB,CAAC,CAAC;YACH,OAAO,MAAM,IAAI,EAAE,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGM,KAAK,CAAC,QAAQ;QACnB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QACrE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC/D,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QACtF,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,SAAS;QACf,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,OAAO;QACb,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAC9B,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;IACpD,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;CACF;AA7QD,8BA6QC"}