"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LSPClient = void 0;
const child_process_1 = require("child_process");
const events_1 = require("events");
const path = __importStar(require("path"));
const node_1 = require("vscode-jsonrpc/node");
const utils_1 = require("./utils");
class LSPClient extends events_1.EventEmitter {
    constructor(workspaceRoot, serverConfig, language) {
        super();
        this.workspaceRoot = workspaceRoot;
        this.serverConfig = serverConfig;
        this.process = null;
        this.connection = null;
        this.isInitialized = false;
        this.isShuttingDown = false;
        this.workspaceRoot = path.normalize(workspaceRoot);
        this.language = language;
        this.readyPromise = new Promise(resolve => {
            this.readyResolve = resolve;
        });
    }
    async start() {
        if (this.process) {
            throw new Error('LSP client already started');
        }
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.cleanup();
                reject(new Error(`LSP server startup timeout (30s) for ${this.language}`));
            }, 30000);
            try {
                const [command, ...args] = this.serverConfig.command;
                if (!command) {
                    clearTimeout(timeout);
                    reject(new Error('No command specified in server config'));
                    return;
                }
                const allArgs = [...args, ...(this.serverConfig.args || [])];
                this.process = (0, child_process_1.spawn)(command, allArgs, {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    cwd: this.workspaceRoot,
                    env: { ...process.env, ...this.serverConfig.env },
                    shell: true,
                });
                this.process.on('error', (error) => {
                    console.error(`[LSP] Process spawn error for ${this.language}:`, error);
                    clearTimeout(timeout);
                    this.cleanup();
                    reject(error);
                });
                this.process.stderr?.on('data', (data) => {
                    console.error(`[LSP stderr] ${data.toString()}`);
                });
                this.process.on('exit', (code, signal) => {
                    console.error(`[LSP] Process for ${this.language} exited with code ${code}, signal ${signal}`);
                    if (!this.isShuttingDown) {
                        this.emit('exit', code, signal);
                    }
                    this.cleanup();
                });
                this.connection = (0, node_1.createMessageConnection)(new node_1.StreamMessageReader(this.process.stdout), new node_1.StreamMessageWriter(this.process.stdin));
                let readinessTimeout;
                this.connection.onNotification('$/progress', (params) => {
                    if (this.isInitialized && params.value.kind === 'end') {
                        console.error(`[LSP] Received progress end notification for ${this.language}, marking as ready.`);
                        if (readinessTimeout)
                            clearTimeout(readinessTimeout);
                        this.readyResolve();
                    }
                });
                this.connection.listen();
                this.initialize()
                    .then(() => {
                    clearTimeout(timeout);
                    this.isInitialized = true;
                    // Fallback: if no progress message, assume ready after a delay
                    readinessTimeout = setTimeout(() => {
                        console.error(`[LSP] Readiness fallback timer triggered for ${this.language}.`);
                        this.readyResolve();
                    }, 5000);
                    resolve();
                })
                    .catch((error) => {
                    console.error(`[LSP] Initialization failed for ${this.language}:`, error);
                    clearTimeout(timeout);
                    this.cleanup();
                    reject(error);
                });
            }
            catch (error) {
                clearTimeout(timeout);
                reject(error);
            }
        });
    }
    async initialize() {
        if (!this.connection) {
            throw new Error("Connection is not established.");
        }
        const rootUri = (0, utils_1.pathToUri)(this.workspaceRoot);
        const params = {
            processId: process.pid,
            clientInfo: { name: 'smart-audit-lsp-client', version: '0.1.0' },
            rootUri: rootUri,
            workspaceFolders: [{
                    uri: rootUri,
                    name: `${this.language}-workspace`
                }],
            capabilities: {
                textDocument: {
                    synchronization: {
                        dynamicRegistration: false,
                        willSave: false,
                        willSaveWaitUntil: false,
                        didSave: true
                    },
                    definition: { linkSupport: false },
                    references: {
                        dynamicRegistration: false,
                    },
                    rename: { prepareSupport: false },
                    documentSymbol: {
                        symbolKind: {
                            valueSet: [
                                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23,
                                24, 25, 26,
                            ],
                        },
                        hierarchicalDocumentSymbolSupport: true,
                    },
                    completion: {
                        completionItem: {
                            snippetSupport: true,
                        },
                    },
                    signatureHelp: {},
                    diagnostic: {
                        dynamicRegistration: false,
                        relatedDocumentSupport: false,
                    },
                },
                workspace: {
                    workspaceFolders: true,
                },
            },
            initializationOptions: {
                settings: {}
            }
        };
        await this.connection.sendRequest('initialize', params);
        await this.connection.sendNotification('initialized');
    }
    async openDocument(uri, content, languageId) {
        if (!this.isInitialized || !this.connection) {
            throw new Error('LSP client not initialized');
        }
        const params = {
            textDocument: {
                uri,
                languageId: languageId,
                version: 1,
                text: content,
            },
        };
        await this.connection.sendNotification('textDocument/didOpen', params);
    }
    async getDefinition(uri, position) {
        if (!this.connection)
            throw new Error('LSP client not connected');
        try {
            const result = await this.connection.sendRequest('textDocument/definition', {
                textDocument: { uri },
                position,
            });
            return Array.isArray(result) ? result[0] || null : result || null;
        }
        catch (error) {
            console.error(`[LSP] Error in getDefinition for ${uri}:`, JSON.stringify(error, null, 2));
            throw error;
        }
    }
    async getReferences(uri, position) {
        if (!this.connection)
            throw new Error('LSP client not connected');
        try {
            const result = await this.connection.sendRequest('textDocument/references', {
                textDocument: { uri },
                position,
                context: { includeDeclaration: true },
            });
            return result || [];
        }
        catch (error) {
            console.error(`[LSP] Error in getReferences for ${uri}:`, JSON.stringify(error, null, 2));
            throw error; // Re-throw the original error
        }
    }
    async getDocumentSymbols(uri) {
        if (!this.connection)
            throw new Error('LSP client not connected');
        try {
            const result = await this.connection.sendRequest('textDocument/documentSymbol', {
                textDocument: { uri },
            });
            return result || [];
        }
        catch (error) {
            console.error(`[LSP] Error in getDocumentSymbols for ${uri}:`, JSON.stringify(error, null, 2));
            throw error;
        }
    }
    async shutdown() {
        if (this.isShuttingDown || !this.process || !this.connection)
            return;
        this.isShuttingDown = true;
        try {
            if (this.isInitialized) {
                await this.connection.sendRequest('shutdown', undefined, 5000);
                this.connection.sendNotification('exit');
            }
        }
        catch (error) {
            console.error(`[LSP] Error during 'shutdown' request for ${this.language}:`, error);
        }
        finally {
            this.forceKill();
            this.cleanup();
        }
    }
    forceKill() {
        if (this.process && !this.process.killed) {
            this.process.kill('SIGKILL');
        }
    }
    cleanup() {
        if (this.connection) {
            this.connection.dispose();
            this.connection = null;
        }
        this.process = null;
        this.isInitialized = false;
        this.isShuttingDown = false;
    }
    isReady() {
        return this.isInitialized && !this.isShuttingDown;
    }
    async waitUntilReady() {
        return this.readyPromise;
    }
}
exports.LSPClient = LSPClient;
//# sourceMappingURL=lsp-client.js.map