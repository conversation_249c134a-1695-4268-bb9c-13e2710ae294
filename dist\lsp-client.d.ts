import { EventEmitter } from 'events';
import { LSPServerConfig, Position, Location, DocumentSymbol } from './types';
export declare class LSPClient extends EventEmitter {
    private workspaceRoot;
    private serverConfig;
    private process;
    private connection;
    private isInitialized;
    private isShuttingDown;
    private language;
    private readyPromise;
    private readyResolve;
    constructor(workspaceRoot: string, serverConfig: LSPServerConfig, language: string);
    start(): Promise<void>;
    private initialize;
    openDocument(uri: string, content: string, languageId: string): Promise<void>;
    getDefinition(uri: string, position: Position): Promise<Location | null>;
    getReferences(uri: string, position: Position): Promise<Location[]>;
    getDocumentSymbols(uri: string): Promise<DocumentSymbol[]>;
    shutdown(): Promise<void>;
    private forceKill;
    private cleanup;
    isReady(): boolean;
    waitUntilReady(): Promise<void>;
}
