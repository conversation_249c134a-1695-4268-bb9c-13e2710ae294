{"version": 3, "file": "lsp-manager.js", "sourceRoot": "", "sources": ["../src/lsp-manager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAyC;AAEzC,2CAA6B;AAC7B,uCAAyB;AAEzB,iDAA2E;AAE3E,MAAa,UAAU;IAMrB;QALQ,eAAU,GAAG,IAAI,GAAG,EAAkC,CAAC;QACvD,wBAAmB,GAAG,IAAI,GAAG,EAAyB,CAAC;QAEvD,qBAAgB,GAA2B,EAAE,CAAC;QAGpD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAChC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAEO,qBAAqB;QAC3B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAC1D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACpC,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;IAEO,UAAU;QAChB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACjE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,gCAAgC,UAAU,EAAE,CAAC,CAAC;QAChE,CAAC;QACD,MAAM,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAW,CAAC;IACzC,CAAC;IAEM,mBAAmB,CAAC,QAAgB;QACzC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IAC5C,CAAC;IAEO,2BAA2B,CAAC,YAA6B;QAC/D,6DAA6D;QAC7D,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,aAAqB,EAAE,QAAgB;QAC/D,MAAM,iBAAiB,GAAG,GAAG,aAAa,IAAI,QAAQ,EAAE,CAAC;QAEzD,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACpD,OAAO,CAAC,KAAK,CAAC,sDAAsD,QAAQ,OAAO,aAAa,EAAE,CAAC,CAAC;YACpG,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACtD,OAAO,CAAC,KAAK,CAAC,0CAA0C,QAAQ,OAAO,aAAa,YAAY,CAAC,CAAC;YAClG,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC5D,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;gBACpG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC5D,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvD,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC/B,OAAO,CAAC,KAAK,CAAC,2BAA2B,QAAQ,iBAAiB,aAAa,oCAAoC,CAAC,CAAC;gBACrH,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,MAAM,qBAAqB,GAAG,CAAC,KAAK,IAAI,EAAE;YACxC,IAAI,CAAC;gBACH,OAAO,CAAC,KAAK,CAAC,wCAAwC,aAAa,kBAAkB,QAAQ,EAAE,CAAC,CAAC;gBAEjG,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC;gBACrG,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,OAAO,CAAC,IAAI,CAAC,qDAAqD,QAAQ,aAAa,CAAC,CAAC;oBACzF,OAAO;gBACT,CAAC;gBAED,MAAM,iBAAiB,GAAoB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;gBAEpF,IAAI,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACjD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;oBAC5E,IAAI,kBAAkB,EAAE,CAAC;wBACvB,OAAO,CAAC,KAAK,CAAC,+CAA+C,kBAAkB,EAAE,CAAC,CAAC;wBACnF,iBAAiB,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC;wBACtD,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,kBAAkB,EAAE,CAAC,CAAC;oBAC9E,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,IAAI,CAAC,0EAA0E,aAAa,GAAG,CAAC,CAAC;oBAC3G,CAAC;gBACH,CAAC;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,GAAG,EAAqB,CAAC;gBACnF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBAE5C,OAAO,CAAC,KAAK,CAAC,oCAAoC,QAAQ,OAAO,aAAa,EAAE,CAAC,CAAC;gBAClF,MAAM,MAAM,GAAG,IAAI,sBAAS,CAAC,aAAa,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;gBACzE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACrB,OAAO,CAAC,KAAK,CAAC,2BAA2B,QAAQ,OAAO,aAAa,uBAAuB,CAAC,CAAC;oBAC9F,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC;gBACH,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;gBACrB,OAAO,CAAC,KAAK,CAAC,2BAA2B,QAAQ,0CAA0C,CAAC,CAAC;gBAC7F,MAAM,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC9B,OAAO,CAAC,KAAK,CAAC,2BAA2B,QAAQ,YAAY,CAAC,CAAC;YACjE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,oDAAoD,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBACtF,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACnD,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;QACvE,MAAM,qBAAqB,CAAC;QAE5B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACxD,IAAI,YAAY,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;YACxF,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,mCAAmC,QAAQ,OAAO,aAAa,sCAAsC,CAAC,CAAC;YACpH,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,wBAAwB;QAC7B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,aAAqB,EAAE,UAAkB;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,0BAA0B,UAAU,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,iBAAiB,GAAG,GAAG,aAAa,IAAI,QAAQ,EAAE,CAAC;QACzD,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,8BAA8B,aAAa,uCAAuC,CAAC,CAAC;QACtG,CAAC;QAED,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,kBAAkB,QAAQ,qCAAqC,aAAa,GAAG,CAAC,CAAC;QACnG,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QACpD,IAAI,WAAW,GAAG,SAAS,CAAC;QAC5B,OAAO,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE,CAAC;YAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;YACjE,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,OAAO,WAAW,CAAC;YACrB,CAAC;YACD,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC1C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,aAAqB;QAClD,MAAM,YAAY,GAAG,MAAM,IAAA,4BAAa,EAAC,aAAa,CAAC,CAAC;QACxD,MAAM,UAAU,GAAG,MAAM,IAAA,yCAA0B,EAAC,aAAa,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;QAErF,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YACxC,IAAI,IAAI,EAAE,CAAC;gBACT,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,MAAM,gBAAgB,GAAoB,EAAE,CAAC;QAC7C,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACnD,OAAO,CAAC,KAAK,CAAC,yCAAyC,SAAS,EAAE,CAAC,CAAC;YACpE,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;gBACrC,OAAO,CAAC,KAAK,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;gBAC5D,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QACD,MAAM,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;CACF;AA7LD,gCA6LC"}