{"version": 3, "file": "file-scanner.js", "sourceRoot": "", "sources": ["../src/file-scanner.ts"], "names": [], "mappings": ";;;;;AAkCA,sCAiBC;AAKD,gEAoEC;AA5HD,+CAA2D;AAC3D,+CAAqD;AACrD,yCAA0C;AAC1C,oDAA4B;AAE5B,0BAA0B;AAC1B,MAAM,uBAAuB,GAAG;IAC9B,cAAc;IACd,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,KAAK;IACL,QAAQ;IACR,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,UAAU;IACV,aAAa;IACb,MAAM;IACN,OAAO;IACP,QAAQ;IACR,SAAS;IACT,OAAO;IACP,OAAO;IACP,WAAW;IACX,WAAW;CACZ,CAAC;AAEF;;GAEG;AACI,KAAK,UAAU,aAAa,CAAC,WAAmB;IACrD,MAAM,EAAE,GAAG,IAAA,gBAAM,GAAE,CAAC;IAEpB,uBAAuB;IACvB,EAAE,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAEhC,yCAAyC;IACzC,MAAM,aAAa,GAAG,IAAA,gBAAI,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;IACtD,IAAI,CAAC;QACH,MAAM,IAAA,iBAAM,EAAC,aAAa,EAAE,oBAAS,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,gBAAgB,GAAG,MAAM,IAAA,mBAAQ,EAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAChE,EAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kDAAkD;IACpD,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,0BAA0B,CAC9C,OAAe,EACf,QAAQ,GAAG,EAAE,EACb,YAAwC,EACxC,KAAK,GAAG,KAAK;IAEb,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;IAErC,KAAK,UAAU,aAAa,CAC1B,WAAmB,EACnB,YAAoB,EACpB,YAAY,GAAG,EAAE;QAEjB,IAAI,YAAY,GAAG,QAAQ;YAAE,OAAO;QAEpC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAA,kBAAO,EAAC,WAAW,CAAC,CAAC;YAC3C,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,MAAM,CAAC,KAAK,CAClB,sBAAsB,WAAW,YAAY,YAAY,YAAY,OAAO,CAAC,MAAM,aAAa,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CACvH,CAAC;YACJ,CAAC;YAED,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,QAAQ,GAAG,IAAA,gBAAI,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBAC1C,MAAM,iBAAiB,GAAG,YAAY,CAAC,CAAC,CAAC,IAAA,gBAAI,EAAC,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAE3E,+EAA+E;gBAC/E,MAAM,cAAc,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC7D,IAAI,YAAY,EAAE,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;oBAC1C,IAAI,KAAK,EAAE,CAAC;wBACV,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,iBAAiB,IAAI,CAAC,CAAC;oBACzE,CAAC;oBACD,SAAS;gBACX,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAI,EAAC,QAAQ,CAAC,CAAC;oBAEtC,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;wBAC3B,IAAI,KAAK,EAAE,CAAC;4BACV,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,iBAAiB,IAAI,CAAC,CAAC;wBAC3E,CAAC;wBACD,MAAM,aAAa,CAAC,QAAQ,EAAE,YAAY,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC;oBACrE,CAAC;yBAAM,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;wBAC7B,MAAM,GAAG,GAAG,IAAA,mBAAO,EAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,eAAe;wBACzD,IAAI,KAAK,EAAE,CAAC;4BACV,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,iBAAiB,GAAG,KAAK,CAAC,CAAC;wBACtE,CAAC;wBACD,IAAI,GAAG,EAAE,CAAC;4BACR,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;4BACpB,IAAI,KAAK,EAAE,CAAC;gCACV,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;4BACpD,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,WAAW,KAAK,KAAK,IAAI,CAAC,CAAC;YAC3E,OAAO;QACT,CAAC;IACH,CAAC;IAED,MAAM,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAChC,OAAO,UAAU,CAAC;AACpB,CAAC"}