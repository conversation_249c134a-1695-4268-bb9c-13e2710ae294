#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LSPMCPServer = void 0;
const index_js_1 = require("@modelcontextprotocol/sdk/server/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const types_js_1 = require("@modelcontextprotocol/sdk/types.js");
const tools_js_1 = require("./tools.js");
const path = __importStar(require("path"));
const fs_1 = require("fs");
class LSPMCPServer {
    constructor() {
        this.server = new index_js_1.Server({
            name: 'smart-audit-lsp-mcp',
            version: '1.0.0',
        }, {
            capabilities: {
                tools: {},
            },
        });
        this.lspTools = new tools_js_1.LSPTools();
        this.setupHandlers();
    }
    setupHandlers() {
        // 工具列表处理器
        this.server.setRequestHandler(types_js_1.ListToolsRequestSchema, async () => {
            return {
                tools: this.getToolDefinitions(),
            };
        });
        // 工具调用处理器
        this.server.setRequestHandler(types_js_1.CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            if (!args) {
                throw new Error('Missing arguments');
            }
            try {
                let result;
                switch (name) {
                    case 'init_workspace':
                        result = await this.lspTools.initWorkspace(args);
                        break;
                    case 'go_to_definition': {
                        const params = await this.mapToLspParamsWithKeyword(args);
                        result = await this.lspTools.goToDefinition(params);
                        break;
                    }
                    case 'find_references': {
                        const params = await this.mapToLspParamsWithKeyword(args);
                        result = await this.lspTools.findReferences(params);
                        break;
                    }
                    default:
                        throw new Error(`Unknown tool: ${name}`);
                }
                return {
                    content: [
                        {
                            type: 'text',
                            text: JSON.stringify(result, null, 2),
                        },
                    ],
                };
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                console.error(`[MCP] Tool execution failed: ${name}`, error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: JSON.stringify({
                                success: false,
                                error: errorMessage
                            }, null, 2),
                        },
                    ],
                    isError: true,
                };
            }
        });
        // 优雅关闭处理
        process.on('SIGINT', this.shutdown.bind(this));
        process.on('SIGTERM', this.shutdown.bind(this));
    }
    async mapToLspParamsWithKeyword(args) {
        const { workspaceRoot, targetFile, line, keyword } = args;
        if (!workspaceRoot || !targetFile || !line || !keyword) {
            throw new Error('Missing required arguments: workspaceRoot, targetFile, line, and keyword are required.');
        }
        const absolutePath = path.join(workspaceRoot, targetFile);
        const fileContent = await fs_1.promises.readFile(absolutePath, 'utf-8');
        const lines = fileContent.split('\n');
        const targetLine = lines[line - 1];
        if (!targetLine) {
            throw new Error(`Line ${line} not found in file ${targetFile}.`);
        }
        // Use regex with word boundaries to match whole words only
        const regex = new RegExp(`\\b${keyword}\\b`);
        const match = targetLine.match(regex);
        if (!match || typeof match.index === 'undefined') {
            throw new Error(`Keyword "${keyword}" not found on line ${line} in file ${targetFile}.`);
        }
        const row = match.index + 1;
        return this.mapToLspParams({ ...args, row });
    }
    mapToLspParams(args) {
        if (args.workspaceRoot && args.targetFile) {
            return {
                workspaceRoot: args.workspaceRoot,
                targetFile: args.targetFile,
                line: args.line,
                row: args.row || args.column,
            };
        }
        if (args.file_path) {
            const filePath = args.file_path;
            const workspaceRoot = this.lspTools.resolveWorkspace(filePath);
            if (!workspaceRoot) {
                throw new Error(`Could not resolve workspace for file: ${filePath}. Please initialize the workspace first.`);
            }
            const targetFile = path.relative(workspaceRoot, filePath);
            return {
                workspaceRoot,
                targetFile,
                line: args.line,
                row: args.row || args.column,
            };
        }
        throw new Error('Invalid arguments. Must provide either {workspaceRoot, targetFile} or {file_path}.');
    }
    getToolDefinitions() {
        return [
            {
                name: 'init_workspace',
                description: '初始化/预热指定工作区的LSP服务端',
                inputSchema: {
                    type: 'object',
                    properties: {
                        workspaceRoot: {
                            type: 'string',
                            description: '工作区根目录路径'
                        },
                        language: {
                            type: 'string',
                            description: '要启动的LSP服务的语言'
                        }
                    },
                    required: ['workspaceRoot', 'language']
                }
            },
            {
                name: 'go_to_definition',
                description: '跳转到符号定义位置，返回定义位置所在的文件以及行号和列号',
                inputSchema: {
                    type: 'object',
                    properties: {
                        workspaceRoot: {
                            type: 'string',
                            description: '工作区根目录路径'
                        },
                        targetFile: {
                            type: 'string',
                            description: '目标文件路径（相对于工作区根目录）'
                        },
                        line: {
                            type: 'number',
                            description: '行号（1-based）'
                        },
                        keyword: {
                            type: 'string',
                            description: '需要查找的变量或内容'
                        },
                    },
                    required: ['workspaceRoot', 'targetFile', 'line', 'keyword']
                }
            },
            {
                name: 'find_references',
                description: '查找指定符号的所有引用位置，返回所有引用位置的文件、行号和列号',
                inputSchema: {
                    type: 'object',
                    properties: {
                        workspaceRoot: {
                            type: 'string',
                            description: '工作区根目录路径'
                        },
                        targetFile: {
                            type: 'string',
                            description: '目标文件路径（相对于工作区根目录）'
                        },
                        line: {
                            type: 'number',
                            description: '行号（1-based）'
                        },
                        keyword: {
                            type: 'string',
                            description: '需要查找的变量或内容'
                        },
                    },
                    required: ['workspaceRoot', 'targetFile', 'line', 'keyword']
                }
            },
        ];
    }
    async run() {
        const transport = new stdio_js_1.StdioServerTransport();
        await this.server.connect(transport);
        console.error('[MCP] Smart Audit LSP Server started');
    }
    async shutdown() {
        console.error('[MCP] Shutting down server...');
        try {
            await this.lspTools.shutdown();
            process.exit(0);
        }
        catch (error) {
            console.error('[MCP] Error during shutdown:', error);
            process.exit(1);
        }
    }
}
exports.LSPMCPServer = LSPMCPServer;
// 启动服务器
if (require.main === module) {
    const server = new LSPMCPServer();
    server.run().catch((error) => {
        console.error('[MCP] Failed to start server:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=server.js.map