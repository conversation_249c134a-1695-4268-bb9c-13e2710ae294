import { LSPClient } from './lsp-client';
export declare class LSPManager {
    private workspaces;
    private initializingClients;
    private config;
    private languageMappings;
    constructor();
    private buildLanguageMappings;
    private loadConfig;
    getLanguageFromFile(filePath: string): string | null;
    private getLanguageFromServerConfig;
    initializeWorkspace(workspaceRoot: string, language: string): Promise<string[]>;
    getInitializedWorkspaces(): string[];
    getClient(workspaceRoot: string, targetFile: string): Promise<LSPClient>;
    private findCompileCommandsDir;
    private findAllLanguages;
    shutdown(): Promise<void>;
}
