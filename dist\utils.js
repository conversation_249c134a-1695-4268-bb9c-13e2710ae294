"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.pathToUri = pathToUri;
exports.uriToPath = uriToPath;
const node_url_1 = require("node:url");
/**
 * Convert a file path to a proper file:// URI
 * Handles Windows paths correctly (e.g., C:\path -> file:///C:/path)
 */
function pathToUri(filePath) {
    // Normalize the path to use forward slashes and ensure it's absolute
    const normalizedPath = filePath.replace(/\\/g, '/');
    return (0, node_url_1.pathToFileURL)(normalizedPath).toString();
}
/**
 * Convert a file:// URI to a file path
 * Handles Windows URIs correctly (e.g., file:///C:/path -> C:\path)
 */
function uriToPath(uri) {
    return (0, node_url_1.fileURLToPath)(uri);
}
//# sourceMappingURL=utils.js.map