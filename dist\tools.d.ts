interface ToolResult<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
interface DefinitionResult {
    file: string;
    line: number;
    row: number;
    text: string;
}
interface Reference {
    file: string;
    line: number;
    row: number;
    text: string;
}
interface ReferencesResult {
    count: number;
    references: Reference[];
}
interface SymbolInfo {
    name: string;
    kind: string;
    line: number;
    row: number;
}
interface SymbolsResult {
    count: number;
    symbols: SymbolInfo[];
}
export declare class LSPTools {
    private lspManager;
    constructor();
    resolveWorkspace(filePath: string): string | undefined;
    private validateParams;
    initWorkspace(params: {
        workspaceRoot: string;
        language: string;
    }): Promise<ToolResult<null>>;
    goToDefinition(params: {
        workspaceRoot: string;
        targetFile: string;
        line: number;
        row: number;
    }): Promise<ToolResult<DefinitionResult>>;
    findReferences(params: {
        workspaceRoot: string;
        targetFile: string;
        line: number;
        row: number;
    }): Promise<ToolResult<ReferencesResult>>;
    getSymbols(params: {
        workspaceRoot: string;
        targetFile: string;
    }): Promise<ToolResult<SymbolsResult>>;
    shutdown(): Promise<void>;
}
export {};
