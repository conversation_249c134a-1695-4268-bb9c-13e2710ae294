"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LSPTools = void 0;
const lsp_manager_1 = require("./lsp-manager");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const types_1 = require("./types");
const utils_1 = require("./utils");
class LSPTools {
    constructor() {
        this.lspManager = new lsp_manager_1.LSPManager();
    }
    resolveWorkspace(filePath) {
        const workspaces = this.lspManager.getInitializedWorkspaces();
        const normalizedFilePath = path.normalize(filePath);
        for (const ws of workspaces) {
            const normalizedWs = path.normalize(ws);
            if (normalizedFilePath.startsWith(normalizedWs)) {
                return ws;
            }
        }
        return undefined;
    }
    validateParams(workspaceRoot, targetFile, line, row) {
        if (!fs.existsSync(workspaceRoot)) {
            throw new Error(`Workspace does not exist: ${workspaceRoot}`);
        }
        if (targetFile) {
            const absolutePath = path.isAbsolute(targetFile) ? targetFile : path.join(workspaceRoot, targetFile);
            if (!fs.existsSync(absolutePath)) {
                throw new Error(`File does not exist: ${absolutePath}`);
            }
        }
        if (line !== undefined && line < 1)
            throw new Error('Line number must be >= 1');
        if (row !== undefined && row < 1)
            throw new Error('Character position must be >= 1');
    }
    async initWorkspace(params) {
        const { workspaceRoot, language } = params;
        this.validateParams(workspaceRoot);
        if (!language) {
            throw new Error("The 'language' parameter is required.");
        }
        const initializedLanguages = await this.lspManager.initializeWorkspace(workspaceRoot, language.toLowerCase());
        if (initializedLanguages.length === 0) {
            return {
                success: false,
                message: `Could not initialize LSP server for ${language}. Check server configurations.`
            };
        }
        return {
            success: true,
            message: `LSP client for ${language} initialized successfully in ${workspaceRoot}.`
        };
    }
    async goToDefinition(params) {
        const { workspaceRoot, targetFile, line, row } = params;
        this.validateParams(workspaceRoot, targetFile, line, row);
        const client = await this.lspManager.getClient(workspaceRoot, targetFile);
        const absolutePath = path.isAbsolute(targetFile) ? targetFile : path.join(workspaceRoot, targetFile);
        const uri = (0, utils_1.pathToUri)(absolutePath);
        const language = this.lspManager.getLanguageFromFile(targetFile);
        const content = fs.readFileSync(absolutePath, 'utf-8');
        await client.openDocument(uri, content, language);
        const definition = await client.getDefinition(uri, { line: line - 1, character: row - 1 });
        if (!definition) {
            return { success: false, error: 'No definition found' };
        }
        const defPath = (0, utils_1.uriToPath)(definition.uri);
        const defContent = fs.readFileSync(defPath, 'utf-8');
        const defLines = defContent.split('\n');
        const lineText = defLines[definition.range.start.line]?.trim() || '';
        return {
            success: true,
            data: {
                file: defPath,
                line: definition.range.start.line + 1,
                row: definition.range.start.character + 1,
                text: lineText,
            }
        };
    }
    async findReferences(params) {
        const { workspaceRoot, targetFile, line, row } = params;
        this.validateParams(workspaceRoot, targetFile, line, row);
        const client = await this.lspManager.getClient(workspaceRoot, targetFile);
        const absolutePath = path.isAbsolute(targetFile) ? targetFile : path.join(workspaceRoot, targetFile);
        const uri = (0, utils_1.pathToUri)(absolutePath);
        const language = this.lspManager.getLanguageFromFile(targetFile);
        const content = fs.readFileSync(absolutePath, 'utf-8');
        await client.openDocument(uri, content, language);
        const references = await client.getReferences(uri, { line: line - 1, character: row - 1 });
        const referencesByFile = new Map();
        for (const ref of references) {
            if (!referencesByFile.has(ref.uri)) {
                referencesByFile.set(ref.uri, []);
            }
            referencesByFile.get(ref.uri).push(ref);
        }
        const referencesWithText = [];
        for (const [uri, refs] of referencesByFile.entries()) {
            const filePath = (0, utils_1.uriToPath)(uri);
            const content = fs.readFileSync(filePath, 'utf-8');
            const lines = content.split('\n');
            for (const ref of refs) {
                const lineText = lines[ref.range.start.line]?.trim() || '';
                referencesWithText.push({
                    file: filePath,
                    line: ref.range.start.line + 1,
                    row: ref.range.start.character + 1,
                    text: lineText
                });
            }
        }
        referencesWithText.sort((a, b) => {
            if (a.file < b.file)
                return -1;
            if (a.file > b.file)
                return 1;
            if (a.line < b.line)
                return -1;
            if (a.line > b.line)
                return 1;
            return a.row - b.row;
        });
        return {
            success: true,
            data: {
                count: references.length,
                references: referencesWithText
            }
        };
    }
    async getSymbols(params) {
        const { workspaceRoot, targetFile } = params;
        this.validateParams(workspaceRoot, targetFile);
        const client = await this.lspManager.getClient(workspaceRoot, targetFile);
        const absolutePath = path.isAbsolute(targetFile) ? targetFile : path.join(workspaceRoot, targetFile);
        const uri = (0, utils_1.pathToUri)(absolutePath);
        const language = this.lspManager.getLanguageFromFile(targetFile);
        const content = fs.readFileSync(absolutePath, 'utf-8');
        await client.openDocument(uri, content, language);
        const symbols = await client.getDocumentSymbols(uri);
        return {
            success: true,
            data: {
                count: symbols.length,
                symbols: symbols.map((symbol) => ({
                    name: symbol.name,
                    kind: types_1.SymbolKind[symbol.kind] || 'Unknown',
                    line: symbol.range.start.line + 1,
                    row: symbol.range.start.character + 1,
                }))
            }
        };
    }
    async shutdown() {
        await this.lspManager.shutdown();
    }
}
exports.LSPTools = LSPTools;
//# sourceMappingURL=tools.js.map