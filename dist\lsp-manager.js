"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LSPManager = void 0;
const lsp_client_1 = require("./lsp-client");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const file_scanner_1 = require("./file-scanner");
class LSPManager {
    constructor() {
        this.workspaces = new Map();
        this.initializingClients = new Map();
        this.languageMappings = {};
        this.config = this.loadConfig();
        this.buildLanguageMappings();
    }
    buildLanguageMappings() {
        for (const server of this.config.servers) {
            const language = this.getLanguageFromServerConfig(server);
            for (const ext of server.extensions) {
                this.languageMappings[`.${ext}`] = language;
            }
        }
    }
    loadConfig() {
        const configPath = path.join(__dirname, '..', 'lsp-config.json');
        if (!fs.existsSync(configPath)) {
            throw new Error(`LSP config file not found at ${configPath}`);
        }
        const rawConfig = fs.readFileSync(configPath, 'utf-8');
        return JSON.parse(rawConfig);
    }
    getLanguageFromFile(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        return this.languageMappings[ext] || null;
    }
    getLanguageFromServerConfig(serverConfig) {
        // Use the first extension as the primary language identifier
        return (serverConfig.extensions[0] || 'unknown').toLowerCase();
    }
    async initializeWorkspace(workspaceRoot, language) {
        const initializationKey = `${workspaceRoot}:${language}`;
        if (this.initializingClients.has(initializationKey)) {
            console.error(`[LSPManager] Waiting for ongoing initialization of ${language} in ${workspaceRoot}`);
            await this.initializingClients.get(initializationKey);
            console.error(`[LSPManager] Ongoing initialization of ${language} in ${workspaceRoot} finished.`);
            const workspaceClients = this.workspaces.get(workspaceRoot);
            if (workspaceClients && workspaceClients.has(language) && workspaceClients.get(language)?.isReady()) {
                return [language];
            }
        }
        const workspaceClients = this.workspaces.get(workspaceRoot);
        if (workspaceClients && workspaceClients.has(language)) {
            const client = workspaceClients.get(language);
            if (client && client.isReady()) {
                console.error(`[LSPManager] Client for ${language} in workspace ${workspaceRoot} is already initialized and ready.`);
                return [language];
            }
        }
        const initializationPromise = (async () => {
            try {
                console.error(`[LSPManager] Initializing workspace: ${workspaceRoot} for language: ${language}`);
                const serverConfig = this.config.servers.find(s => this.getLanguageFromServerConfig(s) === language);
                if (!serverConfig) {
                    console.warn(`[LSPManager] No server command found for language ${language} in config.`);
                    return;
                }
                const finalServerConfig = JSON.parse(JSON.stringify(serverConfig));
                if (finalServerConfig.command.includes('clangd')) {
                    const compileCommandsDir = await this.findCompileCommandsDir(workspaceRoot);
                    if (compileCommandsDir) {
                        console.error(`[LSPManager] Found compile_commands.json in ${compileCommandsDir}`);
                        finalServerConfig.args = finalServerConfig.args || [];
                        finalServerConfig.args.push(`--compile-commands-dir=${compileCommandsDir}`);
                    }
                    else {
                        console.warn(`[LSPManager] 'compile_commands.json' not found for clangd in workspace ${workspaceRoot}.`);
                    }
                }
                const clients = this.workspaces.get(workspaceRoot) || new Map();
                this.workspaces.set(workspaceRoot, clients);
                console.error(`[LSPManager] Creating client for ${language} in ${workspaceRoot}`);
                const client = new lsp_client_1.LSPClient(workspaceRoot, finalServerConfig, language);
                client.on('exit', () => {
                    console.error(`[LSPManager] Client for ${language} at ${workspaceRoot} exited unexpectedly.`);
                    clients.delete(language);
                });
                await client.start();
                console.error(`[LSPManager] Client for ${language} starting... waiting for it to be ready.`);
                await client.waitUntilReady();
                clients.set(language, client);
                console.error(`[LSPManager] Client for ${language} is ready.`);
            }
            catch (error) {
                console.error(`[LSPManager] Failed to start client for language ${language}:`, error);
                const clients = this.workspaces.get(workspaceRoot);
                if (clients) {
                    clients.delete(language);
                }
            }
            finally {
                this.initializingClients.delete(initializationKey);
            }
        })();
        this.initializingClients.set(initializationKey, initializationPromise);
        await initializationPromise;
        const finalClients = this.workspaces.get(workspaceRoot);
        if (finalClients && finalClients.has(language) && finalClients.get(language)?.isReady()) {
            return [language];
        }
        else {
            console.warn(`[LSPManager] Initialization for ${language} in ${workspaceRoot} completed, but client is not ready.`);
            return [];
        }
    }
    getInitializedWorkspaces() {
        return Array.from(this.workspaces.keys());
    }
    async getClient(workspaceRoot, targetFile) {
        const language = this.getLanguageFromFile(targetFile);
        if (!language) {
            throw new Error(`Unsupported file type: ${targetFile}`);
        }
        const initializationKey = `${workspaceRoot}:${language}`;
        if (this.initializingClients.has(initializationKey)) {
            await this.initializingClients.get(initializationKey);
        }
        const workspaceClients = this.workspaces.get(workspaceRoot);
        if (!workspaceClients) {
            throw new Error(`Workspace not initialized: ${workspaceRoot}. Please call 'init_workspace' first.`);
        }
        const client = workspaceClients.get(language);
        if (!client || !client.isReady()) {
            throw new Error(`LSP client for ${language} is not available or not ready in ${workspaceRoot}.`);
        }
        return client;
    }
    async findCompileCommandsDir(startPath) {
        let currentPath = startPath;
        while (currentPath && path.dirname(currentPath) !== currentPath) {
            const filePath = path.join(currentPath, 'compile_commands.json');
            if (fs.existsSync(filePath)) {
                return currentPath;
            }
            currentPath = path.dirname(currentPath);
        }
        return null;
    }
    async findAllLanguages(workspaceRoot) {
        const ignoreFilter = await (0, file_scanner_1.loadGitignore)(workspaceRoot);
        const extensions = await (0, file_scanner_1.scanDirectoryForExtensions)(workspaceRoot, 20, ignoreFilter);
        const languages = new Set();
        for (const ext of extensions) {
            const lang = this.languageMappings[ext];
            if (lang) {
                languages.add(lang);
            }
        }
        console.error(`[LSPManager] Found languages: ${Array.from(languages).join(', ')}`);
        return languages;
    }
    async shutdown() {
        const shutdownPromises = [];
        for (const [workspace, clients] of this.workspaces) {
            console.error(`[LSPManager] Shutting down workspace: ${workspace}`);
            for (const [lang, client] of clients) {
                console.error(`[LSPManager] Shutting down client: ${lang}`);
                shutdownPromises.push(client.shutdown());
            }
        }
        await Promise.allSettled(shutdownPromises);
        this.workspaces.clear();
    }
}
exports.LSPManager = LSPManager;
//# sourceMappingURL=lsp-manager.js.map