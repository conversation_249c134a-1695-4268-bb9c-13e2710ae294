import ignore from 'ignore';
/**
 * Load gitignore patterns and create an ignore filter
 */
export declare function loadGitignore(projectPath: string): Promise<ReturnType<typeof ignore>>;
/**
 * Recursively scan directory for file extensions
 */
export declare function scanDirectoryForExtensions(dirPath: string, maxDepth?: number, ignoreFilter?: ReturnType<typeof ignore>, debug?: boolean): Promise<Set<string>>;
