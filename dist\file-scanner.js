"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadGitignore = loadGitignore;
exports.scanDirectoryForExtensions = scanDirectoryForExtensions;
const promises_1 = require("node:fs/promises");
const promises_2 = require("node:fs/promises");
const node_path_1 = require("node:path");
const ignore_1 = __importDefault(require("ignore"));
// Default ignore patterns
const DEFAULT_IGNORE_PATTERNS = [
    'node_modules',
    '.git',
    '.svn',
    '.hg',
    'dist',
    'build',
    'out',
    'target',
    'bin',
    'obj',
    '.next',
    '.nuxt',
    'coverage',
    '.nyc_output',
    'temp',
    'cache',
    '.cache',
    '.vscode',
    '.idea',
    '*.log',
    '.DS_Store',
    'Thumbs.db',
];
/**
 * Load gitignore patterns and create an ignore filter
 */
async function loadGitignore(projectPath) {
    const ig = (0, ignore_1.default)();
    // Add default patterns
    ig.add(DEFAULT_IGNORE_PATTERNS);
    // Add .gitignore patterns if file exists
    const gitignorePath = (0, node_path_1.join)(projectPath, '.gitignore');
    try {
        await (0, promises_2.access)(gitignorePath, promises_2.constants.F_OK);
        const gitignoreContent = await (0, promises_1.readFile)(gitignorePath, 'utf-8');
        ig.add(gitignoreContent);
    }
    catch (error) {
        // File doesn't exist or can't be read - that's ok
    }
    return ig;
}
/**
 * Recursively scan directory for file extensions
 */
async function scanDirectoryForExtensions(dirPath, maxDepth = 20, ignoreFilter, debug = false) {
    const extensions = new Set();
    async function scanDirectory(currentPath, currentDepth, relativePath = '') {
        if (currentDepth > maxDepth)
            return;
        try {
            const entries = await (0, promises_1.readdir)(currentPath);
            if (debug) {
                process.stderr.write(`Scanning directory ${currentPath} (depth: ${currentDepth}), found ${entries.length} entries: ${entries.join(', ')}\n`);
            }
            for (const entry of entries) {
                const fullPath = (0, node_path_1.join)(currentPath, entry);
                const entryRelativePath = relativePath ? (0, node_path_1.join)(relativePath, entry) : entry;
                // Skip if ignored - normalize path separators for cross-platform compatibility
                const normalizedPath = entryRelativePath.replace(/\\/g, '/');
                if (ignoreFilter?.ignores(normalizedPath)) {
                    if (debug) {
                        process.stderr.write(`Skipping ignored entry: ${entryRelativePath}\n`);
                    }
                    continue;
                }
                try {
                    const fileStat = await (0, promises_1.stat)(fullPath);
                    if (fileStat.isDirectory()) {
                        if (debug) {
                            process.stderr.write(`Recursing into directory: ${entryRelativePath}\n`);
                        }
                        await scanDirectory(fullPath, currentDepth + 1, entryRelativePath);
                    }
                    else if (fileStat.isFile()) {
                        const ext = (0, node_path_1.extname)(entry).toLowerCase(); // Keep the dot
                        if (debug) {
                            process.stderr.write(`Found file: ${entry}, extension: "${ext}"\n`);
                        }
                        if (ext) {
                            extensions.add(ext);
                            if (debug) {
                                process.stderr.write(`Added extension: ${ext}\n`);
                            }
                        }
                    }
                }
                catch (error) {
                    process.stderr.write(`Error processing ${fullPath}: ${error}\n`);
                }
            }
        }
        catch (error) {
            process.stderr.write(`Error reading directory ${currentPath}: ${error}\n`);
            return;
        }
    }
    await scanDirectory(dirPath, 0);
    return extensions;
}
//# sourceMappingURL=file-scanner.js.map